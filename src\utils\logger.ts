/**
 * Système de logging sécurisé pour FloraSynth
 * 
 * Fonctionnalités :
 * - Logs conditionnels selon l'environnement
 * - Suppression automatique des données sensibles
 * - Niveaux de log configurables
 * - Formatage cohérent des messages
 */

// Types pour le système de logging
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  CRITICAL = 4
}

export interface LogContext {
  component?: string;
  action?: string;
  userId?: string;
  sessionId?: string;
  timestamp?: Date;
}

export interface LogEntry {
  level: LogLevel;
  message: string;
  context?: LogContext;
  data?: any;
  timestamp: Date;
}

// Configuration du logger
class Logger {
  private isDevelopment: boolean;
  private minLogLevel: LogLevel;
  private sensitiveFields: string[];

  constructor() {
    this.isDevelopment = import.meta.env.DEV;
    this.minLogLevel = this.isDevelopment ? LogLevel.DEBUG : LogLevel.WARN;
    this.sensitiveFields = [
      'password',
      'token',
      'apiKey',
      'email',
      'uid',
      'displayName',
      'phoneNumber',
      'photoURL',
      'accessToken',
      'refreshToken'
    ];
  }

  /**
   * Nettoie les données sensibles d'un objet
   */
  private sanitizeData(data: any): any {
    if (!data || typeof data !== 'object') {
      return data;
    }

    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeData(item));
    }

    const sanitized: any = {};
    for (const [key, value] of Object.entries(data)) {
      if (this.sensitiveFields.some(field => 
        key.toLowerCase().includes(field.toLowerCase())
      )) {
        sanitized[key] = '[DONNÉES_SENSIBLES_MASQUÉES]';
      } else if (typeof value === 'object') {
        sanitized[key] = this.sanitizeData(value);
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  /**
   * Formate un message de log avec contexte
   */
  private formatMessage(level: LogLevel, message: string, context?: LogContext): string {
    const timestamp = new Date().toISOString();
    const levelName = LogLevel[level];
    const component = context?.component || 'App';
    const action = context?.action || 'Unknown';

    return `[${timestamp}] ${levelName} [${component}:${action}] ${message}`;
  }

  /**
   * Détermine si un log doit être affiché
   */
  private shouldLog(level: LogLevel): boolean {
    return level >= this.minLogLevel;
  }

  /**
   * Log de niveau DEBUG - Développement uniquement
   */
  debug(message: string, context?: LogContext, data?: any): void {
    if (!this.shouldLog(LogLevel.DEBUG)) return;

    const formattedMessage = this.formatMessage(LogLevel.DEBUG, message, context);
    const sanitizedData = this.sanitizeData(data);

    console.log(`🔍 ${formattedMessage}`, sanitizedData || '');
  }

  /**
   * Log de niveau INFO - Informations générales
   */
  info(message: string, context?: LogContext, data?: any): void {
    if (!this.shouldLog(LogLevel.INFO)) return;

    const formattedMessage = this.formatMessage(LogLevel.INFO, message, context);
    const sanitizedData = this.sanitizeData(data);

    console.info(`ℹ️ ${formattedMessage}`, sanitizedData || '');
  }

  /**
   * Log de niveau WARN - Avertissements
   */
  warn(message: string, context?: LogContext, data?: any): void {
    if (!this.shouldLog(LogLevel.WARN)) return;

    const formattedMessage = this.formatMessage(LogLevel.WARN, message, context);
    const sanitizedData = this.sanitizeData(data);

    console.warn(`⚠️ ${formattedMessage}`, sanitizedData || '');
  }

  /**
   * Log de niveau ERROR - Erreurs récupérables
   */
  error(message: string, context?: LogContext, error?: Error | any): void {
    if (!this.shouldLog(LogLevel.ERROR)) return;

    const formattedMessage = this.formatMessage(LogLevel.ERROR, message, context);
    const sanitizedError = this.sanitizeData(error);

    console.error(`❌ ${formattedMessage}`, sanitizedError || '');

    // En production, envoyer à un service de monitoring
    if (!this.isDevelopment) {
      this.sendToMonitoring({
        level: LogLevel.ERROR,
        message,
        context,
        data: sanitizedError,
        timestamp: new Date()
      });
    }
  }

  /**
   * Log de niveau CRITICAL - Erreurs critiques
   */
  critical(message: string, context?: LogContext, error?: Error | any): void {
    const formattedMessage = this.formatMessage(LogLevel.CRITICAL, message, context);
    const sanitizedError = this.sanitizeData(error);

    console.error(`🚨 ${formattedMessage}`, sanitizedError || '');

    // Toujours envoyer les erreurs critiques au monitoring
    this.sendToMonitoring({
      level: LogLevel.CRITICAL,
      message,
      context,
      data: sanitizedError,
      timestamp: new Date()
    });
  }

  /**
   * Logs spécialisés pour l'authentification
   */
  auth = {
    loginStart: (method: string) => {
      this.debug('Début de connexion', { 
        component: 'Auth', 
        action: 'LoginStart' 
      }, { method });
    },

    loginSuccess: (userId: string) => {
      this.info('Connexion réussie', { 
        component: 'Auth', 
        action: 'LoginSuccess',
        userId: '[MASQUÉ]' // Toujours masquer l'ID utilisateur
      });
    },

    loginError: (error: any, method: string) => {
      this.error('Échec de connexion', { 
        component: 'Auth', 
        action: 'LoginError' 
      }, { method, error });
    },

    logoutSuccess: () => {
      this.info('Déconnexion réussie', { 
        component: 'Auth', 
        action: 'LogoutSuccess' 
      });
    },

    stateChange: (isAuthenticated: boolean) => {
      this.debug('Changement d\'état d\'authentification', { 
        component: 'Auth', 
        action: 'StateChange' 
      }, { isAuthenticated });
    }
  };

  /**
   * Envoie les logs critiques à un service de monitoring
   * (À implémenter avec Sentry, LogRocket, etc.)
   */
  private sendToMonitoring(logEntry: LogEntry): void {
    // TODO: Implémenter l'envoi vers un service de monitoring
    // Exemple avec Sentry :
    // Sentry.captureException(logEntry);
    
    // Pour l'instant, on stocke localement en développement
    if (this.isDevelopment) {
      const logs = JSON.parse(localStorage.getItem('florasynth_logs') || '[]');
      logs.push(logEntry);
      // Garder seulement les 100 derniers logs
      if (logs.length > 100) {
        logs.splice(0, logs.length - 100);
      }
      localStorage.setItem('florasynth_logs', JSON.stringify(logs));
    }
  }

  /**
   * Récupère les logs stockés localement (développement)
   */
  getLogs(): LogEntry[] {
    if (!this.isDevelopment) return [];
    return JSON.parse(localStorage.getItem('florasynth_logs') || '[]');
  }

  /**
   * Nettoie les logs stockés localement
   */
  clearLogs(): void {
    if (this.isDevelopment) {
      localStorage.removeItem('florasynth_logs');
    }
  }
}

// Instance singleton du logger
export const logger = new Logger();

// Exports pour faciliter l'utilisation
export default logger;
