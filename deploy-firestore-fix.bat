@echo off
echo ========================================
echo 🔧 CORRECTION FIRESTORE - VIOLET RIKITA
echo ========================================
echo.

echo 📊 Étape 1: Vérification des fichiers...
if not exist "firestore.indexes.json" (
    echo ❌ Erreur: firestore.indexes.json introuvable
    pause
    exit /b 1
)

if not exist "firestore.rules" (
    echo ❌ Erreur: firestore.rules introuvable  
    pause
    exit /b 1
)

echo ✅ Fichiers trouvés

echo.
echo 📊 Étape 2: Validation de la syntaxe JSON...
firebase firestore:indexes --help >nul 2>&1
if errorlevel 1 (
    echo ❌ Erreur: Firebase CLI non installé ou non configuré
    echo Installez Firebase CLI: npm install -g firebase-tools
    echo Connectez-vous: firebase login
    pause
    exit /b 1
)

echo ✅ Firebase CLI configuré

echo.
echo 📊 Étape 3: Déploiement des règles Firestore...
firebase deploy --only firestore:rules
if errorlevel 1 (
    echo ❌ Erreur lors du déploiement des règles
    pause
    exit /b 1
)

echo ✅ Règles Firestore déployées

echo.
echo 📊 Étape 4: Déploiement des index Firestore...
firebase deploy --only firestore:indexes
if errorlevel 1 (
    echo ❌ Erreur lors du déploiement des index
    pause
    exit /b 1
)

echo ✅ Index Firestore déployés

echo.
echo 📊 Étape 5: Vérification des index...
echo Les index sont en cours de création (5-15 minutes)...
firebase firestore:indexes

echo.
echo ========================================
echo ✅ DÉPLOIEMENT TERMINÉ
echo ========================================
echo.
echo 📋 Actions suivantes:
echo 1. Attendre la création des index (5-15 min)
echo 2. Vérifier dans Firebase Console
echo 3. Recharger l'application
echo 4. Tester les fonctionnalités
echo.
echo 🔗 Firebase Console:
echo https://console.firebase.google.com/project/florasynth-a461d/firestore/indexes
echo.

pause
