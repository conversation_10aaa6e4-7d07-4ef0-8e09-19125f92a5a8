# Script de deploiement Firestore avec configuration projet
# Version avec gestion automatique du projet Firebase

$PROJECT_ID = "florasynth-a461d"

Write-Host "CORRECTION FIRESTORE - VIOLET RIKITA" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green
Write-Host "Projet: $PROJECT_ID" -ForegroundColor Cyan
Write-Host ""

# Verification des fichiers
Write-Host "1. Verification des fichiers..." -ForegroundColor Yellow

if (!(Test-Path "firestore.indexes.json")) {
    Write-Host "ERREUR: firestore.indexes.json introuvable" -ForegroundColor Red
    pause
    exit 1
}

if (!(Test-Path "firestore.rules")) {
    Write-Host "ERREUR: firestore.rules introuvable" -ForegroundColor Red
    pause
    exit 1
}

Write-Host "OK: Fichiers trouves" -ForegroundColor Green

# Verification Firebase CLI
Write-Host "2. Verification Firebase CLI..." -ForegroundColor Yellow

try {
    $version = firebase --version
    Write-Host "OK: Firebase CLI detecte - $version" -ForegroundColor Green
} catch {
    Write-Host "ERREUR: Firebase CLI non installe" -ForegroundColor Red
    Write-Host "Installez avec: npm install -g firebase-tools" -ForegroundColor White
    pause
    exit 1
}

# Verification de l'authentification
Write-Host "3. Verification de l'authentification..." -ForegroundColor Yellow

try {
    firebase projects:list --project $PROJECT_ID | Out-Null
    Write-Host "OK: Authentifie sur Firebase" -ForegroundColor Green
} catch {
    Write-Host "ERREUR: Non authentifie sur Firebase" -ForegroundColor Red
    Write-Host "Connectez-vous avec: firebase login" -ForegroundColor White
    pause
    exit 1
}

# Configuration du projet actif
Write-Host "4. Configuration du projet..." -ForegroundColor Yellow

try {
    firebase use $PROJECT_ID
    Write-Host "OK: Projet $PROJECT_ID configure" -ForegroundColor Green
} catch {
    Write-Host "ATTENTION: Impossible de configurer le projet automatiquement" -ForegroundColor Magenta
    Write-Host "Utilisation du parametre --project a la place" -ForegroundColor Yellow
}

# Deploiement des regles
Write-Host "5. Deploiement des regles..." -ForegroundColor Yellow

$rulesCmd = "firebase deploy --only firestore:rules --project $PROJECT_ID"
Invoke-Expression $rulesCmd

if ($LASTEXITCODE -eq 0) {
    Write-Host "OK: Regles deployees" -ForegroundColor Green
} else {
    Write-Host "ERREUR: Echec deploiement regles" -ForegroundColor Red
    Write-Host "Voulez-vous continuer quand meme? (y/n)" -ForegroundColor Yellow
    $continue = Read-Host
    if ($continue -ne "y" -and $continue -ne "Y") {
        exit 1
    }
}

# Deploiement des index
Write-Host "6. Deploiement des index..." -ForegroundColor Yellow

$indexCmd = "firebase deploy --only firestore:indexes --project $PROJECT_ID"
Invoke-Expression $indexCmd

if ($LASTEXITCODE -eq 0) {
    Write-Host "OK: Index deployes" -ForegroundColor Green
} else {
    Write-Host "ERREUR: Echec deploiement index" -ForegroundColor Red
    pause
    exit 1
}

# Verification des index
Write-Host "7. Verification des index..." -ForegroundColor Yellow
firebase firestore:indexes --project $PROJECT_ID

# Fin
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "DEPLOIEMENT TERMINE!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Actions suivantes:" -ForegroundColor White
Write-Host "1. Attendre la creation des index (5-15 min)" -ForegroundColor Yellow
Write-Host "2. Verifier dans Firebase Console" -ForegroundColor Yellow
Write-Host "3. Recharger l'application" -ForegroundColor Yellow
Write-Host "4. Tester les fonctionnalites" -ForegroundColor Yellow
Write-Host ""
Write-Host "Console Firebase:" -ForegroundColor White
Write-Host "https://console.firebase.google.com/project/$PROJECT_ID/firestore/indexes" -ForegroundColor Blue
Write-Host ""

pause
