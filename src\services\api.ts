import { initializeApp } from "firebase/app";
import { getA<PERSON>, Google<PERSON>uth<PERSON><PERSON><PERSON>, signInWithPopup, signOut } from "firebase/auth";
import {
  getFirestore,
  collection,
  doc,
  query,
  where,
  orderBy,
  onSnapshot,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc
} from "firebase/firestore";
import {
  getStorage,
  ref as storageRef,
  uploadBytes,
  getDownloadURL
} from "firebase/storage";

import { GoogleGenAI, Type } from "@google/genai";
import { Plant, DiagnosticRecord, GeminiDiagnosis } from '../types';
import { notificationService } from './notificationService';
import { logger } from '../utils/logger';
import { authMonitoring } from '../utils/authMonitoring';

import { FERTILIZERS_DATABASE, findFertilizersBySymptoms } from '../data/fertilizers';

// This file centralizes all external API interactions.

// --- FIREBASE SETUP ---
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID,
};

// Validation des variables d'environnement Firebase
const requiredEnvVars = [
  'VITE_FIREBASE_API_KEY',
  'VITE_FIREBASE_AUTH_DOMAIN',
  'VITE_FIREBASE_PROJECT_ID',
  'VITE_FIREBASE_STORAGE_BUCKET',
  'VITE_FIREBASE_MESSAGING_SENDER_ID',
  'VITE_FIREBASE_APP_ID'
];

console.log('🔍 Debug - Variables d\'environnement:');
for (const envVar of requiredEnvVars) {
  const value = import.meta.env[envVar];
  console.log(`${envVar}:`, value ? 'DÉFINIE' : 'MANQUANTE');
  if (!value) {
    console.error(`❌ Variable d'environnement manquante: ${envVar}`);
    console.log('🔍 Toutes les variables disponibles:', JSON.stringify(import.meta.env, null, 2));
    throw new Error(`Variable d'environnement manquante: ${envVar}`);
  }
}

const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);


// --- GEMINI SETUP ---
// API key must be in environment variables (e.g., .env file)
const geminiApiKey = import.meta.env.VITE_GEMINI_API_KEY;
if (!geminiApiKey) {
    throw new Error("VITE_GEMINI_API_KEY is not set in environment variables");
}
const genAI = new GoogleGenAI({ apiKey: geminiApiKey });
const geminiModel = 'gemini-2.5-flash';

// --- AUTHENTICATION ---
export const provider = new GoogleAuthProvider();



export const signInWithGoogle = async () => {
  logger.auth.loginStart('Google');
  authMonitoring.loginStart('Google');

  try {
    logger.debug('Ouverture de la popup Google', {
      component: 'Auth',
      action: 'PopupOpen'
    });

    const result = await signInWithPopup(auth, provider);
    const user = result.user;

    if (user) {
      logger.auth.loginSuccess(user.uid);
      authMonitoring.loginSuccess(user.uid, 'Google');

      // Vérifier l'état d'authentification immédiatement
      logger.debug('Vérification de l\'état d\'authentification', {
        component: 'Auth',
        action: 'StateVerification'
      });

      const currentUser = auth.currentUser;
      logger.debug('État d\'authentification vérifié', {
        component: 'Auth',
        action: 'StateVerified'
      }, { hasCurrentUser: !!currentUser });

      return { success: true, user };
    } else {
      const errorMsg = 'Aucun utilisateur retourné';
      logger.auth.loginError(new Error(errorMsg), 'Google');
      authMonitoring.loginFailure('no-user-returned', 'Google');
      return { success: false, error: errorMsg };
    }
  } catch (error: any) {
    logger.auth.loginError(error, 'Google');
    console.error("❌ Code d'erreur:", error.code);
    console.error("❌ Message d'erreur:", error.message);

    // Gestion spécifique des erreurs courantes
    if (error.code === 'auth/popup-closed-by-user') {
      logger.info('Popup fermée par l\'utilisateur', {
        component: 'Auth',
        action: 'PopupClosed'
      });
      authMonitoring.loginFailure(error.code, 'Google');
      return { success: false, error: "Connexion annulée par l'utilisateur" };
    } else if (error.code === 'auth/popup-blocked') {
      logger.warn('Popup bloquée par le navigateur', {
        component: 'Auth',
        action: 'PopupBlocked'
      });
      authMonitoring.loginFailure(error.code, 'Google');
      return { success: false, error: "Popup bloquée. Veuillez autoriser les popups pour ce site." };
    } else if (error.code === 'auth/cancelled-popup-request') {
      logger.info('Demande de popup annulée', {
        component: 'Auth',
        action: 'PopupCancelled'
      });
      authMonitoring.loginFailure(error.code, 'Google');
      return { success: false, error: "Connexion annulée" };
    } else if (error.code === 'auth/unauthorized-domain') {
      logger.error('Domaine non autorisé dans Firebase', {
        component: 'Auth',
        action: 'UnauthorizedDomain'
      }, { domain: window.location.hostname });
      authMonitoring.loginFailure(error.code, 'Google');
      return { success: false, error: "Domaine non autorisé. Vérifiez la configuration Firebase." };
    }

    // Erreur générique
    authMonitoring.loginFailure(error.code || 'unknown-error', 'Google');
    return { success: false, error: error.message || "Erreur de connexion" };
  }
};

export const signOutUser = async () => {
  try {
    logger.debug('Début de déconnexion', {
      component: 'Auth',
      action: 'LogoutStart'
    });

    await signOut(auth);
    logger.auth.logoutSuccess();
    authMonitoring.logout();
  } catch (error: any) {
    logger.error('Erreur lors de la déconnexion', {
      component: 'Auth',
      action: 'LogoutError'
    }, error);
  }
};

// --- FIRESTORE SERVICE (PLANTS) ---
const plantsCol = (userId: string) => collection(db, 'users', userId, 'plants');

export const getPlants = (userId: string, callback: (plants: Plant[]) => void): (() => void) => {
  const q = query(plantsCol(userId), orderBy('createdAt', 'desc'));
  const unsubscribe = onSnapshot(q, (snapshot) => {
    const plantsData = snapshot.docs.map(docSnap => ({ id: docSnap.id, ...docSnap.data() } as Plant));
    callback(plantsData);
  });
  return unsubscribe;
};

export const getPlant = async (userId: string, plantId: string): Promise<Plant | null> => {
  const plantRef = doc(db, 'users', userId, 'plants', plantId);
  const plantSnap = await getDoc(plantRef);
  return plantSnap.exists() ? { id: plantSnap.id, ...plantSnap.data() } as Plant : null;
};

export const addPlant = async (userId: string, plantData: Omit<Plant, 'id'>) => {
  return await addDoc(plantsCol(userId), {
    ...plantData,
    createdAt: new Date()
  });
};

export const updatePlant = async (userId: string, plantId: string, updatedData: Partial<Plant>) => {
  const plantRef = doc(db, 'users', userId, 'plants', plantId);
  return await updateDoc(plantRef, updatedData);
};

export const deletePlant = async (userId: string, plantId: string) => {
  const plantRef = doc(db, 'users', userId, 'plants', plantId);
  // Note: This doesn't delete subcollections. For a production app, a Cloud Function would be needed for cascading deletes.
  return await deleteDoc(plantRef);
};

// --- FIRESTORE SERVICE (DIAGNOSTIC RECORDS) ---
const diagnosticsCol = (userId: string, plantId: string) => collection(db, 'users', userId, 'plants', plantId, 'diagnostics');

export const getDiagnosticRecords = (userId: string, plantId: string, callback: (records: DiagnosticRecord[]) => void): (() => void) => {
  const q = query(diagnosticsCol(userId, plantId), orderBy('timestamp', 'desc'));
  const unsubscribe = onSnapshot(q, (snapshot) => {
    const recordsData = snapshot.docs.map(docSnap => ({ id: docSnap.id, ...docSnap.data() } as DiagnosticRecord));
    callback(recordsData);
  });
  return unsubscribe;
};

export const addDiagnosticRecord = async (userId: string, plantId: string, recordData: Omit<DiagnosticRecord, 'id'>) => {
  const docRef = await addDoc(diagnosticsCol(userId, plantId), recordData);

  // Créer automatiquement un événement de notification si c'est un diagnostic Gemini
  if (recordData.diagnosis && recordData.plantName) {
    try {
      // Récupérer les diagnostics précédents pour l'analyse intelligente
      const previousDiagnostics = await new Promise<DiagnosticRecord[]>((resolve) => {
        const unsubscribe = getDiagnosticRecords(userId, plantId, (records) => {
          unsubscribe();
          resolve(records.slice(0, 5)); // Les 5 derniers diagnostics
        });
      });

      await notificationService.createEventFromDiagnosis(
        userId,
        plantId,
        recordData.plantName,
        recordData.diagnosis,
        docRef.id,
        previousDiagnostics
      );
    } catch (error) {
      console.error('Erreur lors de la création de l\'événement de notification:', error);
      // Ne pas faire échouer l'ajout du diagnostic si la notification échoue
    }
  }

  return docRef;
};



// --- STORAGE SERVICE ---
export const uploadImage = async (file: File, path: string): Promise<string> => {
  const refPath = storageRef(storage, path);
  await uploadBytes(refPath, file);
  return await getDownloadURL(refPath);
};

// --- GEMINI SERVICE ---
const diagnosisSchema = {
    type: Type.OBJECT,
    properties: {
        isHealthy: { type: Type.BOOLEAN, description: "Is the plant generally healthy?" },
        disease: { type: Type.STRING, description: "Name of the disease or pest, or 'Healthy' if none." },
        description: { type: Type.STRING, description: "A detailed description of the diagnosis." },
        treatmentPlan: {
            type: Type.OBJECT,
            properties: {
                steps: { type: Type.ARRAY, items: { type: Type.STRING }, description: "Step-by-step treatment instructions." },
                treatmentFrequencyDays: { type: Type.NUMBER, description: "How often to apply treatment, in days. 0 if one-time." },
                recommendedProducts: {
                    type: Type.ARRAY,
                    items: {
                        type: Type.OBJECT,
                        properties: {
                            name: { type: Type.STRING, description: "Name of the fertilizer or treatment product" },
                            type: { type: Type.STRING, description: "Type of product (fertilizer, fungicide, etc.)" },
                            dosages: {
                                type: Type.OBJECT,
                                properties: {
                                    pulverisateur_1L: { type: Type.STRING, description: "Dosage for 1L spray bottle" },
                                    pulverisateur_5L: { type: Type.STRING, description: "Dosage for 5L spray bottle" },
                                    arrosoir_11L: { type: Type.STRING, description: "Dosage for 11L watering can" },
                                    arrosoir_13L: { type: Type.STRING, description: "Dosage for 13L watering can" },
                                    pulverisateur_16L: { type: Type.STRING, description: "Dosage for 16L large spray bottle" }
                                }
                            },
                            applicationMethod: { type: Type.STRING, description: "How to apply (foliar spray, soil watering, etc.)" },
                            precautions: { type: Type.STRING, description: "Safety precautions and warnings" }
                        }
                    }
                }
            }
        },
        careTips: { type: Type.ARRAY, items: { type: Type.STRING }, description: "General care tips for this plant." }
    }
};

// Fonction pour générer des recommandations d'engrais contextuelles
const generateFertilizerRecommendations = (plantName: string): string => {
    const recommendations = FERTILIZERS_DATABASE.map(fertilizer => {
        const dosageInfo = fertilizer.dosages.map(d => `${d.methode}: ${d.dose}`).join(', ');
        return `- ${fertilizer.nom} (${fertilizer.composition_npk}): ${dosageInfo}`;
    }).join('\n');

    return `ENGRAIS DISPONIBLES POUR ${plantName.toUpperCase()} :\n${recommendations}`;
};

export const analyzePlantImages = async (base64Images: string[], plantName: string): Promise<GeminiDiagnosis> => {
    const imageParts = base64Images.map(imgData => ({
        inlineData: {
            mimeType: 'image/jpeg',
            data: imgData
        }
    }));

    // Générer les recommandations contextuelles d'engrais
    const fertilizerContext = generateFertilizerRecommendations(plantName);

    const prompt = `Analysez la ou les images fournies d'une plante ${plantName}. Identifiez toute maladie, parasite ou carence nutritionnelle.

${fertilizerContext}

INSTRUCTIONS IMPORTANTES POUR LES DOSAGES :
- Pour chaque produit recommandé, calculez les dosages PRÉCIS pour ces contenants spécifiques :
  * Pulvérisateur 1L : dosage exact en grammes ou millilitres
  * Pulvérisateur 5L : dosage exact en grammes ou millilitres
  * Arrosoir 11L : dosage exact en grammes ou millilitres
  * Arrosoir 13L : dosage exact en grammes ou millilitres
  * Pulvérisateur 16L : dosage exact en grammes ou millilitres

BASE DE CONNAISSANCES ENGRAIS ET TRAITEMENTS :

1. SULFATE DE FER (FeSO4·7H2O) - Anti-chlorose :
   - Pulvérisation foliaire : 2-3g/L (pour chlorose ferrique)
   - Arrosage au sol : 15-20g/m²
   - Cible : Plantes acidophiles, rosiers, arbres fruitiers
   - Symptômes corrigés : Chlorose ferrique (jaunissement feuilles, nervures vertes)
   - Précautions : Éviter plein soleil, peut tacher surfaces

2. SULFATE DE MAGNÉSIUM (MgSO4·7H2O) - Sel d'Epsom :
   - Pulvérisation foliaire : 1 c.à.c/L (5g/L)
   - Arrosage au sol : 20g/10L d'eau
   - Cible : Tomates, rosiers, conifères, légumes
   - Symptômes corrigés : Jaunissement internervaire, nécrose marginale
   - Fréquence : Tous les 15 jours

3. SULFATE DE POTASSIUM (K2SO4) - Potasse :
   - Arrosage : 0.5-1g/L
   - Incorporation sol : 20-40g/m²
   - Cible : Floraison, fructification, résistance maladies
   - Symptômes corrigés : Bords feuilles brûlés, fruits de mauvaise qualité
   - Période : Printemps avant floraison, été pendant fructification

4. URÉE (CO(NH2)2) - Engrais azoté 46% :
   - Pulvérisation diluée : 3-4g/L (avec précaution)
   - Arrosage : 20-30g/m²
   - Cible : Croissance végétative, verdissement
   - Symptômes corrigés : Jaunissement généralisé, croissance lente
   - Précautions : Risque brûlure, éviter temps chaud

5. PHOSPHATE NATUREL (Poudre d'os) - NPK 5-25-0 :
   - Incorporation : 50g/m²
   - Action : Libération lente, développement racinaire
   - Cible : Plantation, floraison, fructification
   - Fréquence : 2 fois par an

6. PURIN D'ORTIE :
   - Arrosage fertilisant : 10% (1L purin + 9L eau)
   - Pulvérisation fortifiante : 5% (0.5L purin + 9.5L eau)
   - Cible : Stimulation croissance, défenses naturelles
   - Fréquence : Tous les 15 jours

7. AMENDEMENT CALCAIRE (Coquilles d'huîtres) :
   - Incorporation : 100-500g/m² selon acidité
   - Action : Correction pH, apport calcium
   - Cible : Sols acides, nécrose apicale tomates
   - Période : Automne/printemps

8. SOUFRE ÉLÉMENTAIRE :
   - Acidification : 30-50g/m²
   - Fongicide : 5g/L (soufre mouillable)
   - Cible : Plantes acidophiles, oïdium, acariens
   - Précautions : Éviter >28°C, irritant respiratoire

9. CORNE BROYÉE - NPK 13-0-0 :
   - Incorporation : 50-75g/m² légumes, 300-500g/arbre
   - Action : Azote libération lente
   - Période : Plantation, entretien annuel

10. SANG SÉCHÉ - NPK 12-14-0 :
    - Épandage : 50g/m² gazon, 75g/m² potager
    - Action : Azote "coup de fouet"
    - Période : Printemps, croissance active

11. ENGRAIS NPK ÉQUILIBRÉ (20-20-20) :
    - Fertigation : Selon fabricant (ex: 800-1200 fois dilué)
    - Action : Nutrition complète
    - Précautions : Risque sur-fertilisation

12. OLIGO-ÉLÉMENTS (Fe, Mn, Zn, Cu, B, Mo) :
    - Application : 5-10g/10L
    - Fréquence : 1-2 fois/an en prévention
    - Cible : Carences multiples, fonctions vitales

TRAITEMENTS NATURELS MALADIES :
- Bouillie bordelaise : Mildiou, tavelure (selon fabricant)
- Bicarbonate de soude : Oïdium (5g/L)
- Décoction de prêle : Fortifiant, fongique préventif
- Savon noir : Pucerons, cochenilles (10-20ml/L)

DIAGNOSTIC SPÉCIALISÉ DES CARENCES :
- AZOTE (N) : Jaunissement généralisé (feuilles anciennes d'abord) → Urée, Sang séché, Corne broyée
- PHOSPHORE (P) : Feuilles pourpres/violacées, mauvaise floraison → Phosphate naturel, NPK
- POTASSIUM (K) : Bords feuilles brûlés/jaunis, fruits de mauvaise qualité → Sulfate de potassium
- MAGNÉSIUM (Mg) : Jaunissement internervaire (nervures vertes) → Sulfate de magnésium
- FER (Fe) : Chlorose ferrique (jeunes feuilles jaunes, nervures vertes) → Sulfate de fer
- CALCIUM (Ca) : Nécrose apicale (cul noir tomates), déformations → Amendement calcaire
- SOUFRE (S) : Jaunissement uniforme jeunes feuilles → Sulfate de magnésium, Soufre élémentaire

ANALYSE EXPERTE REQUISE :
1. Examinez attentivement les symptômes visuels sur les images
2. Identifiez les carences nutritionnelles spécifiques (N, P, K, Mg, Fe, Ca, S)
3. Déterminez si c'est une maladie fongique, bactérienne, virale ou parasitaire
4. Sélectionnez les engrais/traitements les plus appropriés de la liste ci-dessus
5. Calculez les dosages EXACTS pour chaque contenant (1L, 5L, 11L, 13L, 16L)
6. Précisez la méthode d'application (pulvérisation foliaire, arrosage au sol)
7. Indiquez les précautions d'usage et la fréquence d'application
8. Mentionnez la période optimale de traitement

PRIORITÉ AUX SOLUTIONS NATURELLES ET BIOLOGIQUES quand c'est possible.

Fournissez un diagnostic clair, un plan de traitement détaillé avec dosages précis, et des conseils de soin généraux. Si la plante semble en bonne santé, confirmez-le et fournissez des conseils de soins préventifs avec dosages préventifs. Répondez en format JSON selon le schéma fourni. Répondez en français.`;

    const contents = {
        parts: [
            ...imageParts,
            { text: prompt }
        ]
    };

    const response = await genAI.models.generateContent({
        model: geminiModel,
        contents,
        config: {
            responseMimeType: "application/json",
            responseSchema: diagnosisSchema
        }
    });

    try {
        const jsonText = response.text?.trim() || '';
        if (!jsonText) {
            throw new Error("Réponse vide de l'IA");
        }
        return JSON.parse(jsonText) as GeminiDiagnosis;
    } catch (e) {
        console.error("Failed to parse Gemini JSON response:", response.text);
        throw new Error("L'analyse IA a retourné un format invalide.");
    }
};
