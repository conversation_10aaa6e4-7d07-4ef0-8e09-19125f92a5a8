import {
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from './api';
import { Plant, DiagnosticRecord } from '../types';

export interface ArchiveData {
  year: number;
  userId: string;
  plants: Plant[];
  diagnostics: DiagnosticRecord[];
  archivedAt: Timestamp;
  totalPlants: number;
  totalDiagnostics: number;
  geminiAccessible: boolean; // Indique si Gemini peut accéder à ces données
}

export interface ArchiveStats {
  totalArchives: number;
  oldestArchive: number;
  newestArchive: number;
  totalArchivedPlants: number;
  totalArchivedDiagnostics: number;
}

/**
 * Service de gestion de l'archivage automatique annuel
 * Permet l'archivage des données de l'année précédente avec accès Gemini
 */
class ArchiveService {
  
  /**
   * Archive automatiquement les données de l'année précédente
   * Appelé automatiquement chaque 1er janvier
   */
  async archiveYearData(userId: string, year: number): Promise<void> {
    console.log(`🗄️ Début de l'archivage des données ${year} pour l'utilisateur ${userId}`);
    
    try {
      const batch = writeBatch(db);
      
      // Récupérer toutes les plantes de l'utilisateur
      const plantsQuery = query(
        collection(db, 'users', userId, 'plants'),
        where('createdAt', '>=', new Date(year, 0, 1)),
        where('createdAt', '<', new Date(year + 1, 0, 1))
      );
      
      const plantsSnapshot = await getDocs(plantsQuery);
      const plants: Plant[] = plantsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Plant));
      
      // Récupérer tous les diagnostics de l'année
      const diagnostics: DiagnosticRecord[] = [];
      
      for (const plant of plants) {
        const diagnosticsQuery = query(
          collection(db, 'users', userId, 'plants', plant.id, 'diagnostics'),
          where('timestamp', '>=', Timestamp.fromDate(new Date(year, 0, 1))),
          where('timestamp', '<', Timestamp.fromDate(new Date(year + 1, 0, 1))),
          orderBy('timestamp', 'desc')
        );
        
        const diagnosticsSnapshot = await getDocs(diagnosticsQuery);
        const plantDiagnostics = diagnosticsSnapshot.docs.map(doc => ({
          id: doc.id,
          plantId: plant.id,
          ...doc.data()
        } as DiagnosticRecord));
        
        diagnostics.push(...plantDiagnostics);
      }
      
      // Créer l'archive
      const archiveData: ArchiveData = {
        year,
        userId,
        plants,
        diagnostics,
        archivedAt: Timestamp.now(),
        totalPlants: plants.length,
        totalDiagnostics: diagnostics.length,
        geminiAccessible: true // Permet à Gemini d'accéder aux données pour l'apprentissage
      };
      
      // Sauvegarder l'archive
      const archiveRef = doc(db, 'archives', `${userId}_${year}`);
      await setDoc(archiveRef, archiveData);
      
      console.log(`✅ Archivage terminé: ${plants.length} plantes, ${diagnostics.length} diagnostics`);
      
      // Notifier Gemini de la nouvelle archive disponible
      await this.notifyGeminiNewArchive(userId, year, archiveData);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      console.error('❌ Erreur lors de l\'archivage:', errorMessage);
      throw new Error(`Échec de l'archivage des données ${year}: ${errorMessage}`);
    }
  }
  
  /**
   * Récupère les archives d'un utilisateur
   */
  async getUserArchives(userId: string): Promise<ArchiveData[]> {
    try {
      console.log('🔍 Récupération des archives pour userId:', userId);

      const archivesQuery = query(
        collection(db, 'archives'),
        where('userId', '==', userId),
        orderBy('year', 'desc')
      );

      console.log('🔍 Exécution de la requête archives...');
      const snapshot = await getDocs(archivesQuery);

      console.log('🔍 Résultat requête archives:', {
        empty: snapshot.empty,
        size: snapshot.size,
        docs: snapshot.docs.length
      });

      const archives = snapshot.docs.map(doc => doc.data() as ArchiveData);
      console.log('🔍 Archives récupérées:', archives.length, 'archives trouvées');

      return archives;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      console.error('❌ Erreur lors de la récupération des archives:', errorMessage);
      if (error instanceof Error && error.stack) {
        console.error('❌ Stack trace:', String(error.stack));
      }
      return [];
    }
  }
  
  /**
   * Récupère une archive spécifique
   */
  async getArchive(userId: string, year: number): Promise<ArchiveData | null> {
    try {
      const archiveRef = doc(db, 'archives', `${userId}_${year}`);
      const snapshot = await getDoc(archiveRef);

      if (!snapshot.exists()) return null;

      return { id: snapshot.id, ...snapshot.data() } as ArchiveData;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      console.error('❌ Erreur lors de la récupération de l\'archive:', errorMessage);
      return null;
    }
  }
  
  /**
   * Obtient les statistiques des archives
   */
  async getArchiveStats(userId: string): Promise<ArchiveStats> {
    try {
      console.log('🔍 Calcul des statistiques d\'archives pour userId:', userId);
      const archives = await this.getUserArchives(userId);

      console.log('🔍 Archives pour statistiques:', archives.length);

      if (archives.length === 0) {
        console.log('🔍 Aucune archive trouvée, retour des stats par défaut');
        return {
          totalArchives: 0,
          oldestArchive: new Date().getFullYear(),
          newestArchive: new Date().getFullYear(),
          totalArchivedPlants: 0,
          totalArchivedDiagnostics: 0
        };
      }
      
      const years = archives.map(a => a.year);
      const totalPlants = archives.reduce((sum, a) => sum + a.totalPlants, 0);
      const totalDiagnostics = archives.reduce((sum, a) => sum + a.totalDiagnostics, 0);
      
      return {
        totalArchives: archives.length,
        oldestArchive: Math.min(...years),
        newestArchive: Math.max(...years),
        totalArchivedPlants: totalPlants,
        totalArchivedDiagnostics: totalDiagnostics
      };
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      console.error('❌ Erreur lors du calcul des statistiques:', errorMessage);
      return {
        totalArchives: 0,
        oldestArchive: new Date().getFullYear(),
        newestArchive: new Date().getFullYear(),
        totalArchivedPlants: 0,
        totalArchivedDiagnostics: 0
      };
    }
  }
  
  /**
   * Vérifie si l'archivage automatique doit être déclenché
   * À appeler au démarrage de l'application
   */
  async checkAutoArchive(userId: string): Promise<void> {
    const currentYear = new Date().getFullYear();
    const lastYear = currentYear - 1;
    
    // Vérifier si l'archive de l'année dernière existe déjà
    const existingArchive = await this.getArchive(userId, lastYear);
    
    if (!existingArchive) {
      console.log(`🔄 Déclenchement de l'archivage automatique pour ${lastYear}`);
      await this.archiveYearData(userId, lastYear);
    }
  }
  
  /**
   * Crée une archive de test (pour le débogage uniquement)
   */
  async createTestArchive(userId: string, year: number, archiveData: ArchiveData): Promise<void> {
    const archiveRef = doc(db, 'archives', `${userId}_${year}_TEST`);
    await setDoc(archiveRef, {
      ...archiveData,
      isTestArchive: true
    });
    console.log(`✅ Archive de test créée: ${archiveData.totalPlants} plantes, ${archiveData.totalDiagnostics} diagnostics`);
  }

  /**
   * Supprime une archive (pour les tests uniquement)
   */
  async deleteArchive(userId: string, year: number): Promise<void> {
    const archiveRef = doc(db, 'archives', `${userId}_${year}_TEST`);
    await deleteDoc(archiveRef);
    console.log(`✅ Archive supprimée: ${userId}_${year}`);
  }

  /**
   * Notifie Gemini qu'une nouvelle archive est disponible pour l'apprentissage
   */
  private async notifyGeminiNewArchive(userId: string, year: number, archiveData: ArchiveData): Promise<void> {
    try {
      // Créer un résumé des données pour Gemini
      const learningData = {
        year,
        userId,
        summary: {
          totalPlants: archiveData.totalPlants,
          totalDiagnostics: archiveData.totalDiagnostics,
          commonDiseases: this.extractCommonDiseases(archiveData.diagnostics),
          plantTypes: this.extractPlantTypes(archiveData.plants),
          seasonalPatterns: this.extractSeasonalPatterns(archiveData.diagnostics)
        },
        accessibleForLearning: true,
        archivedAt: archiveData.archivedAt
      };
      
      // Sauvegarder les données d'apprentissage pour Gemini
      const geminiLearningRef = doc(db, 'gemini_learning_data', `${userId}_${year}`);
      await setDoc(geminiLearningRef, learningData);
      
      console.log(`🤖 Données d'apprentissage Gemini créées pour ${year}`);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      console.error('❌ Erreur lors de la notification Gemini:', errorMessage);
    }
  }
  
  /**
   * Extrait les maladies les plus communes des diagnostics
   */
  private extractCommonDiseases(diagnostics: DiagnosticRecord[]): { disease: string; count: number }[] {
    const diseaseCount = new Map<string, number>();
    
    diagnostics.forEach(diagnostic => {
      if (diagnostic.diagnosis?.disease) {
        const disease = diagnostic.diagnosis.disease;
        diseaseCount.set(disease, (diseaseCount.get(disease) || 0) + 1);
      }
    });
    
    return Array.from(diseaseCount.entries())
      .map(([disease, count]) => ({ disease, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10); // Top 10 des maladies
  }
  
  /**
   * Extrait les types de plantes les plus communes
   */
  private extractPlantTypes(plants: Plant[]): { type: string; count: number }[] {
    const typeCount = new Map<string, number>();
    
    plants.forEach(plant => {
      const type = plant.species || 'Non spécifié';
      typeCount.set(type, (typeCount.get(type) || 0) + 1);
    });
    
    return Array.from(typeCount.entries())
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count);
  }
  
  /**
   * Extrait les patterns saisonniers des diagnostics
   */
  private extractSeasonalPatterns(diagnostics: DiagnosticRecord[]): { season: string; diseases: string[] }[] {
    const seasonalData = new Map<string, Set<string>>();
    
    diagnostics.forEach(diagnostic => {
      if (diagnostic.timestamp && diagnostic.diagnosis?.disease) {
        const date = diagnostic.timestamp.toDate();
        const month = date.getMonth();
        
        let season: string;
        if (month >= 2 && month <= 4) season = 'Printemps';
        else if (month >= 5 && month <= 7) season = 'Été';
        else if (month >= 8 && month <= 10) season = 'Automne';
        else season = 'Hiver';
        
        if (!seasonalData.has(season)) {
          seasonalData.set(season, new Set());
        }
        seasonalData.get(season)!.add(diagnostic.diagnosis.disease);
      }
    });
    
    return Array.from(seasonalData.entries()).map(([season, diseases]) => ({
      season,
      diseases: Array.from(diseases)
    }));
  }
}

export const archiveService = new ArchiveService();
