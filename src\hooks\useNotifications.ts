import { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from './useAuth';
import { notificationService } from '../services/notificationService';
import {
  DiagnosticEvent,
  UserNotification,
  UserActionHistory,
  NotificationStats,
  DiagnosticEventFilters,
  CreateDiagnosticEventData,
  UpdateDiagnosticEventData
} from '../types/notifications';

/**
 * Hook pour gérer les événements de diagnostic
 */
export const useDiagnosticEvents = (filters?: DiagnosticEventFilters) => {
  const { user } = useAuth();
  const [events, setEvents] = useState<DiagnosticEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!user) {
      setEvents([]);
      setLoading(false);
      return;
    }

    setLoading(true);
    const unsubscribe = notificationService.getDiagnosticEvents(
      user.uid,
      (fetchedEvents) => {
        setEvents(fetchedEvents);
        setLoading(false);
        setError(null);
      },
      filters
    );

    return unsubscribe;
  }, [user, filters]);

  const createEvent = useCallback(async (eventData: CreateDiagnosticEventData) => {
    if (!user) throw new Error('Utilisateur non connecté');
    
    try {
      const eventId = await notificationService.createDiagnosticEvent(eventData);
      return eventId;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors de la création de l\'événement');
      throw err;
    }
  }, [user]);

  const updateEvent = useCallback(async (eventId: string, updateData: UpdateDiagnosticEventData) => {
    if (!user) throw new Error('Utilisateur non connecté');
    
    try {
      await notificationService.updateDiagnosticEvent(user.uid, eventId, updateData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors de la mise à jour de l\'événement');
      throw err;
    }
  }, [user]);

  const completeEvent = useCallback(async (eventId: string) => {
    if (!user) throw new Error('Utilisateur non connecté');
    
    try {
      await notificationService.completeEvent(user.uid, eventId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors de la finalisation de l\'événement');
      throw err;
    }
  }, [user]);

  return {
    events,
    loading,
    error,
    createEvent,
    updateEvent,
    completeEvent
  };
};

/**
 * Hook pour gérer les notifications utilisateur
 */
export const useNotifications = (unreadOnly: boolean = false) => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<UserNotification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!user) {
      setNotifications([]);
      setLoading(false);
      return;
    }

    setLoading(true);
    const unsubscribe = notificationService.getUserNotifications(
      user.uid,
      (fetchedNotifications) => {
        setNotifications(fetchedNotifications);
        setLoading(false);
        setError(null);
      },
      unreadOnly
    );

    return unsubscribe;
  }, [user, unreadOnly]);

  const markAsRead = useCallback(async (notificationId: string) => {
    if (!user) throw new Error('Utilisateur non connecté');
    
    try {
      await notificationService.markNotificationAsRead(user.uid, notificationId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors du marquage de la notification');
      throw err;
    }
  }, [user]);

  const unreadCount = notifications.filter(n => !n.read).length;

  return {
    notifications,
    loading,
    error,
    unreadCount,
    markAsRead
  };
};

/**
 * Hook pour gérer l'historique des actions
 */
export const useActionHistory = (plantId?: string) => {
  const { user } = useAuth();
  const [history, setHistory] = useState<UserActionHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!user) {
      setHistory([]);
      setLoading(false);
      return;
    }

    setLoading(true);
    const unsubscribe = notificationService.getActionHistory(
      user.uid,
      (fetchedHistory) => {
        setHistory(fetchedHistory);
        setLoading(false);
        setError(null);
      },
      plantId
    );

    return unsubscribe;
  }, [user, plantId]);

  return {
    history,
    loading,
    error
  };
};

/**
 * Hook pour les statistiques de notifications
 */
export const useNotificationStats = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<NotificationStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const refreshStats = useCallback(async () => {
    if (!user) {
      setStats(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const fetchedStats = await notificationService.getNotificationStats(user.uid);
      setStats(fetchedStats);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors du chargement des statistiques');
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    if (!user) {
      setStats(null);
      setLoading(false);
      return;
    }

    const loadStats = async () => {
      try {
        setLoading(true);
        const fetchedStats = await notificationService.getNotificationStats(user.uid);
        setStats(fetchedStats);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erreur lors du chargement des statistiques');
      } finally {
        setLoading(false);
      }
    };

    loadStats();
  }, [user]); // Suppression de refreshStats des dépendances

  return {
    stats,
    loading,
    error,
    refreshStats
  };
};

/**
 * Hook pour les événements en attente (dashboard)
 */
export const usePendingEvents = () => {
  const { events, loading, error } = useDiagnosticEvents({ pendingOnly: true });

  // Mémoriser les dates pour éviter les recalculs
  const { todayEvents, overdueEvents, upcomingEvents } = useMemo(() => {
    const today = new Date();
    const todayString = today.toDateString();
    const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);

    const todayEventsFiltered = events.filter(event => {
      const eventDate = event.nextActionDate.toDate();
      return eventDate.toDateString() === todayString;
    });

    const overdueEventsFiltered = events.filter(event => {
      const eventDate = event.nextActionDate.toDate();
      return eventDate < today && event.status !== 'overdue';
    });

    const upcomingEventsFiltered = events.filter(event => {
      const eventDate = event.nextActionDate.toDate();
      return eventDate > today && eventDate <= nextWeek;
    });

    return {
      todayEvents: todayEventsFiltered,
      overdueEvents: overdueEventsFiltered,
      upcomingEvents: upcomingEventsFiltered
    };
  }, [events]); // Seulement dépendant des events

  return {
    allPendingEvents: events,
    todayEvents,
    overdueEvents,
    upcomingEvents,
    loading,
    error
  };
};

/**
 * Hook pour vérifier automatiquement les événements en retard
 */
export const useOverdueChecker = () => {
  const { user } = useAuth();

  const checkOverdueEvents = useCallback(async () => {
    if (!user) return;

    try {
      await notificationService.checkOverdueEvents(user.uid);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      console.error('❌ Erreur lors de la vérification des événements en retard:', errorMessage);
    }
  }, [user]);

  useEffect(() => {
    if (!user) return;

    // Fonction interne pour éviter la dépendance circulaire
    const checkEvents = async () => {
      try {
        await notificationService.checkOverdueEvents(user.uid);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
        console.error('❌ Erreur lors de la vérification des événements en retard:', errorMessage);
      }
    };

    // Vérifier au chargement
    checkEvents();

    // Vérifier toutes les heures
    const interval = setInterval(checkEvents, 60 * 60 * 1000);

    return () => clearInterval(interval);
  }, [user]); // Suppression de checkOverdueEvents des dépendances

  return { checkOverdueEvents };
};

/**
 * Hook pour l'intégration automatique des diagnostics
 */
export const useDiagnosticIntegration = () => {
  const { user } = useAuth();

  const createDiagnosticFromGemini = useCallback(async (
    plantId: string,
    plantName: string,
    diagnosis: any,
    previousDiagnostics?: any[]
  ) => {
    if (!user) throw new Error('Utilisateur non connecté');

    try {
      return await notificationService.createDiagnosticEventFromGemini(
        user.uid,
        plantId,
        plantName,
        diagnosis,
        previousDiagnostics
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      console.error('❌ Erreur lors de la création du diagnostic:', errorMessage);
      throw error;
    }
  }, [user]);

  const completeEventWithHistory = useCallback(async (
    eventId: string,
    treatmentDescription?: string
  ) => {
    if (!user) throw new Error('Utilisateur non connecté');

    try {
      return await notificationService.completeEventWithHistory(
        user.uid,
        eventId,
        treatmentDescription
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      console.error('❌ Erreur lors de la finalisation de l\'événement:', errorMessage);
      throw error;
    }
  }, [user]);

  const addActionToHistory = useCallback(async (
    plantId: string,
    plantName: string,
    actionType: 'diagnostic' | 'treatment_applied' | 'event_completed' | 'event_skipped',
    description: string,
    metadata?: Record<string, any>
  ) => {
    if (!user) throw new Error('Utilisateur non connecté');

    try {
      return await notificationService.addActionHistory({
        userId: user.uid,
        plantId,
        plantName,
        actionType,
        description,
        metadata
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      console.error('❌ Erreur lors de l\'ajout à l\'historique:', errorMessage);
      throw error;
    }
  }, [user]);

  return {
    createDiagnosticFromGemini,
    completeEventWithHistory,
    addActionToHistory
  };
};

/**
 * Hook pour les notifications préventives automatiques
 */
export const usePreventiveNotifications = () => {
  const { user } = useAuth();

  const generatePreventiveNotifications = useCallback(async () => {
    if (!user) return;

    try {
      await notificationService.generatePreventiveNotifications(user.uid);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      console.error('❌ Erreur lors de la génération des notifications préventives:', errorMessage);
    }
  }, [user]);

  // Déclencher automatiquement les notifications préventives
  useEffect(() => {
    if (!user) return;

    // Générer immédiatement
    const generateNotifications = async () => {
      try {
        await notificationService.generatePreventiveNotifications(user.uid);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
        console.error('❌ Erreur lors de la génération des notifications préventives:', errorMessage);
      }
    };

    generateNotifications();

    // Puis toutes les 24 heures
    const interval = setInterval(generateNotifications, 24 * 60 * 60 * 1000);

    return () => clearInterval(interval);
  }, [user]); // Suppression de generatePreventiveNotifications des dépendances

  return {
    generatePreventiveNotifications
  };
};
