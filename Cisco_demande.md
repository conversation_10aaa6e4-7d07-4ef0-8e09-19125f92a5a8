
**Toujours vérifier ta mémoire et vérifier ensuite ce fichier. On ne sait jamais, je peux en cours de route te mettre de nouvelles instructions lorsque toi tu travailles.pas de fichier MD à la racine. Le seul fichier MD qui existe, c'est le mien : `cisco_demande.md`** <PERSON><PERSON>, c'est mon fichier personnel. Tu n'écris rien du tout dedans. Tu ne rédiges absolument rien du tout. C'est mon fichier à moi, celui-là. 




useNotifications.ts:34 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
(anonymous)	@	useNotifications.ts:34
(anonymous)	@	notificationService.ts:177
Promise.then		
getDiagnosticEvents	@	notificationService.ts:172
(anonymous)	@	useNotifications.ts:31
DevT<PERSON><PERSON> has encountered an error

Something went wrong. Try again.
AI tools may generate inaccurate info that doesn't represent Google's views. Data sent to Google may be seen by human reviewers to improve this feature. Open settings or learn more


@react-refresh:228 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
(anonymous)	@	@react-refresh:228
performReactRefresh	@	@react-refresh:217
(anonymous)	@	@react-refresh:608
setTimeout		
(anonymous)	@	@react-refresh:598
validateRefreshBoundaryAndEnqueueUpdate	@	@react-refresh:648
(anonymous)	@	App.tsx:179
(anonymous)	@	client:34
(anonymous)	@	client:213
(anonymous)	@	client:186
queueUpdate	@	client:186
await in queueUpdate		
(anonymous)	@	client:930
handleMessage	@	client:928
await in handleMessage		
(anonymous)	@	client:490
dequeue	@	client:516
(anonymous)	@	client:504
enqueue	@	client:498
(anonymous)	@	client:490
onMessage	@	client:309
(anonymous)	@	client:439
DevTools has encountered an error

Something went wrong. Try again.
AI tools may generate inaccurate info that doesn't represent Google's views. Data sent to Google may be seen by human reviewers to improve this feature. Open settings or learn more



The final argument passed to useEffect changed size between renders. The order and size of this array must remain constant.Previous: [[object Object], async () => {    if (!user) return;    try {      await archiveService.checkAutoArchive(user.uid);      await loadArchives();    } catch (err) {      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue";      console.error("❌ Erreur lors de la vérification d'archivage automatique:", errorMessage);    }  }]Incoming: [[object Object]]

archiveService.ts:93 ❌ Erreur lors de la récupération des archives: Missing or insufficient permissions.

archiveService.ts:95 ❌ Stack trace: FirebaseError: Missing or insufficient permissions.
archiveService.ts:93 ❌ Erreur lors de la récupération des archives: Missing or insufficient permissions.
archiveService.ts:95 ❌ Stack trace: FirebaseError: Missing or insufficient permissions.
15
useNotifications.ts:34 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
archiveService.ts:93 ❌ Erreur lors de la récupération des archives: Missing or insufficient permissions.
archiveService.ts:95 ❌ Stack trace: FirebaseError: Missing or insufficient permissions.
archiveService.ts:93 ❌ Erreur lors de la récupération des archives: Missing or insufficient permissions.
archiveService.ts:95 ❌ Stack trace: FirebaseError: Missing or insufficient permissions.
useArchive.ts:31 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
56
useNotifications.ts:34 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
archiveService.ts?t=1753526094800:93 ❌ Erreur lors de la récupération des archives: Missing or insufficient permissions.
archiveService.ts?t=1753526094800:95 ❌ Stack trace: FirebaseError: Missing or insufficient permissions.
useNotifications.ts:34 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
archiveService.ts?t=1753526094800:93 ❌ Erreur lors de la récupération des archives: Missing or insufficient permissions.
archiveService.ts?t=1753526094800:95 ❌ Stack trace: FirebaseError: Missing or insufficient permissions.
11
useNotifications.ts:34 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
archiveService.ts:144 ❌ Erreur lors de la récupération des archives: Missing or insufficient permissions.
archiveService.ts:146 ❌ Stack trace: FirebaseError: Missing or insufficient permissions.
archiveService.ts:144 ❌ Erreur lors de la récupération des archives: Missing or insufficient permissions.
archiveService.ts:146 ❌ Stack trace: FirebaseError: Missing or insufficient permissions.
84
useNotifications.ts:34 Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
﻿

























